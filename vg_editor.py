from PySide6.QtWidgets import QWidget, QVBoxLayout
from vg_view import VisualGraphView
from vg_scene import VisualGraphScene

class VisualGraphEditor(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_editor()

    def setup_editor(self):
        # 设置窗口位置（x=400, y=400）和大小（宽=1000, 高=720）
        self.setGeometry(400, 400, 1000, 720)
        # 设置窗口标题
        self.setWindowTitle("Visual Graph")

        # 创建垂直布局（默认方向为 TopToBottom，即从上到下排列）
        layout = QVBoxLayout()
        # 移除布局边缘的空白边距（上下左右均设为0）
        layout.setContentsMargins(0, 0, 0, 0)
        # 将布局应用到当前窗口
        self.setLayout(layout)

        # 初始化scene
        self._scene = VisualGraphScene()
        # 初始化view
        self._view = VisualGraphView(self._scene)
        # 添加view到布局
        layout.addWidget(self._view)
