# -*- coding: utf-8 -*-
"""
QGraphicsView 的子类，作为图形场景（Scene）的容器
负责承载图形场景相关的视图逻辑，包括交互控制（平移、缩放等）
"""
import PySide6
from PySide6.QtCore import QEvent
from PySide6.QtGui import QPainter, Qt, QMouseEvent
from PySide6.QtWidgets import QGraphicsView


class VisualGraphView(QGraphicsView):
    """
    继承自 QGraphicsView 的视图容器类
    扩展实现了图形场景的交互功能：
    - 鼠标中键拖动平移画布
    - 鼠标滚轮缩放画布（带缩放范围限制）
    - 中键双击重置缩放
    - 支持橡皮筋选择模式（默认）和手型拖动模式切换
    """

    def __init__(self, scene, parent=None):
        """
        初始化视图容器

        :param scene: 关联的图形场景对象（QGraphicsScene）
        :param parent: 父部件，遵循 Qt 父子对象树机制，默认为 None
        """
        # 调用父类构造函数
        super().__init__(parent)

        # 保存关联的场景对象并设置给视图
        self._scene = scene
        self.setScene(self._scene)

        # 设置渲染提示，提升图形显示质量
        self.setRenderHints(
            QPainter.Antialiasing |  # 启用抗锯齿
            QPainter.TextAntialiasing |  # 文本抗锯齿
            QPainter.SmoothPixmapTransform |  # 平滑像素图变换
            QPainter.LosslessImageRendering  # 无损图像渲染
        )

        # 隐藏滚动条，采用自定义平移缩放逻辑
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # 设置视图更新模式：每次更新整个视口
        self.setViewportUpdateMode(QGraphicsView.FullViewportUpdate)

        # 缩放相关参数
        self._zoom_clamp = [0.2, 2]  # 缩放范围限制 [最小缩放倍数, 最大缩放倍数]
        self._zoom_factor = 1.05  # 单次缩放因子（滚轮每滚动一步的缩放比例）
        self._view_scale = 1.0  # 当前缩放倍数
        self._last_scale = 1.0  # 上一次有效缩放倍数（用于限制范围）

        # 设置变换锚点为鼠标位置（围绕鼠标所在点进行缩放）
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)

        # 默认拖动模式：橡皮筋选择（用于框选场景中的项目）
        self.setDragMode(QGraphicsView.RubberBandDrag)

        # 画布拖动状态标记（是否处于手型拖动模式）
        self._drag_mode = False

    def reset_scale(self):
        """重置视图缩放比例为初始状态（1.0倍）"""
        self.resetTransform()  # 重置所有变换
        self._view_scale = 1.0  # 恢复缩放倍数记录

    def wheelEvent(self, event):
        """
        重写滚轮事件处理函数，实现鼠标滚轮缩放功能

        :param event: 滚轮事件对象（QWheelEvent）
        """
        # 仅在非拖动模式下响应滚轮缩放
        if not self._drag_mode:
            # 判断滚轮方向：上滚放大，下滚缩小
            if event.angleDelta().y() > 0:
                zoom_factor = self._zoom_factor  # 放大因子
            else:
                zoom_factor = 1 / self._zoom_factor  # 缩小因子

            # 计算新的缩放倍数
            new_scale = self._view_scale * zoom_factor

            # 检查是否超出缩放范围限制
            if self._zoom_clamp[0] <= new_scale <= self._zoom_clamp[1]:
                # 在范围内则应用缩放
                self._view_scale = new_scale
                self.scale(zoom_factor, zoom_factor)
            else:
                # 超出范围则保持上一次有效缩放
                self._view_scale = self._last_scale

            # 记录当前有效缩放倍数
            self._last_scale = self._view_scale

    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent) -> None:
        """
        重写鼠标按下事件处理

        :param event: 鼠标事件对象（QMouseEvent）
        """
        # 处理鼠标中键按下（用于启动画布拖动）
        if event.button() == Qt.MiddleButton:
            self.middleButtonPressed(event)

        # 调用父类方法处理其他情况
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent) -> None:
        """
        重写鼠标释放事件处理

        :param event: 鼠标事件对象（QMouseEvent）
        """
        # 处理鼠标中键释放（用于结束画布拖动）
        if event.button() == Qt.MiddleButton:
            self.middleButtonRealeased(event)
        # 处理左键释放（可扩展实现选择等逻辑）
        elif event.button() == Qt.LeftButton:
            self.leftButtonReleased(event)
        # 处理右键释放（可扩展实现上下文菜单等逻辑）
        elif event.button() == Qt.RightButton:
            self.rightButtonReleased(event)

        # 调用父类方法处理其他情况
        super().mouseReleaseEvent(event)

    def mouseDoubleClickEvent(self, event: PySide6.QtGui.QMouseEvent) -> None:
        """
        重写鼠标双击事件处理

        :param event: 鼠标事件对象（QMouseEvent）
        """
        # 中键双击重置缩放
        if event.button() == Qt.MiddleButton:
            self.reset_scale()

        # 调用父类方法处理其他情况
        super().mouseDoubleClickEvent(event)

    def middleButtonPressed(self, event):
        """
        处理鼠标中键按下逻辑：启动画布拖动模式

        :param event: 鼠标事件对象（QMouseEvent）
        """
        # 如果鼠标位置有项目（如节点、连线），则不启动拖动（避免干扰项目操作）
        if self.itemAt(event.pos()) is not None:
            return

        # 释放可能存在的左键按下状态（避免冲突）
        release_event = QMouseEvent(
            QEvent.MouseButtonRelease,  # 事件类型：鼠标释放
            event.pos(),  # 事件位置
            Qt.LeftButton,  # 释放左键
            Qt.NoButton,  # 无按键按下
            event.modifiers()  # 保持修饰键状态
        )
        super().mouseReleaseEvent(release_event)

        # 切换到手型拖动模式
        self.setDragMode(QGraphicsView.ScrollHandDrag)
        self._drag_mode = True

        # 模拟左键按下（触发QGraphicsView的拖动逻辑）
        click_event = QMouseEvent(
            QEvent.MouseButtonPress,  # 事件类型：鼠标按下
            event.pos(),  # 事件位置
            Qt.LeftButton,  # 按下左键
            Qt.NoButton,  # 无其他按键
            event.modifiers()  # 保持修饰键状态
        )
        super().mousePressEvent(click_event)

    def middleButtonRealeased(self, event):
        """
        处理鼠标中键释放逻辑：结束画布拖动模式

        :param event: 鼠标事件对象（QMouseEvent）
        """
        # 释放模拟的左键按下状态
        release_event = QMouseEvent(
            QEvent.MouseButtonRelease,  # 事件类型：鼠标释放
            event.pos(),  # 事件位置
            Qt.LeftButton,  # 释放左键
            Qt.NoButton,  # 无按键按下
            event.modifiers()  # 保持修饰键状态
        )
        super().mouseReleaseEvent(release_event)

        # 切换回橡皮筋选择模式
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self._drag_mode = False

    def leftButtonReleased(self, event):
        """
        左键释放事件处理（预留扩展接口）

        :param event: 鼠标事件对象（QMouseEvent）
        """
        pass  # 可根据需要实现左键释放后的逻辑（如选择完成处理）

    def rightButtonReleased(self, event):
        """
        右键释放事件处理（预留扩展接口）

        :param event: 鼠标事件对象（QMouseEvent）
        """
        pass  # 可根据需要实现右键释放后的逻辑（如显示上下文菜单）