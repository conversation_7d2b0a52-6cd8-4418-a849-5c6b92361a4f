# -*- coding: utf-8 -*-
"""
visual graph 的入口程序
负责启动 Qt 应用并显示主窗口
"""

import sys
from PySide6.QtWidgets import QApplication
from vg_editor import VisualGraphEditor  # 假设自定义模块名为 vg_editor

if __name__ == "__main__":
    # 1. 初始化 Qt 应用（必须传入命令行参数 sys.argv）
    app = QApplication(sys.argv)

    # 2. 创建主窗口并显示
    main_window = VisualGraphEditor()
    main_window.show()  # 关键：必须调用 show() 才会显示窗口

    # 3. 启动事件循环，退出时返回状态码
    sys.exit(app.exec())